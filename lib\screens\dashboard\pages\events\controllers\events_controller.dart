import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/models/events/categories_model.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/cache_keys.dart';

class Eventcontroller extends GetxController implements GetxService {
  final events = <Event>[].obs;
  RxList<MyEventsModel> userEvents = <MyEventsModel>[].obs;
  final logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();

  // Separate loading states for better UX
  RxBool eventsLoading = false.obs;
  RxBool userEventsLoading = false.obs;
  RxBool eventsLoadingMore = false.obs;
  RxBool userEventsLoadingMore = false.obs;

  // Pagination state for events
  RxInt eventsCurrentPage = 0.obs;
  RxInt eventsPageSize = 5.obs;
  RxInt eventsTotalPages = 0.obs;
  RxInt eventsTotal = 0.obs;
  RxBool eventsHasMoreData = true.obs;

  // Pagination state for user events
  RxInt userEventsCurrentPage = 0.obs;
  RxInt userEventsPageSize = 5.obs;
  RxInt userEventsTotalPages = 0.obs;
  RxInt userEventsTotal = 0.obs;
  RxBool userEventsHasMoreData = true.obs;

  // Filter and search state
  final RxString search = "".obs;
  final Rx<int?> filterCategory = Rx<int?>(null);
  final RxString startDate = ''.obs;
  final RxString endDate = ''.obs;
  final RxString status = ''.obs;
  final RxString selectedStatus = "ACTIVE".obs;
  final selectedCategory = Rxn<CategoriesModel>();
  final isApplyingFilters = false.obs;

  // Legacy properties for backward compatibility
  @Deprecated('Use eventsLoading instead')
  RxBool get isLoading => eventsLoading;

  @Deprecated('Use userEventsLoading instead')
  RxBool get isLoadingUser => userEventsLoading;

  @Deprecated('Use eventsCurrentPage instead')
  RxInt get page => eventsCurrentPage;

  @Deprecated('Use userEventsCurrentPage instead')
  RxInt get userpage => userEventsCurrentPage;

  @Deprecated('Use eventsTotalPages instead')
  RxInt get maxPage => eventsTotalPages;

  @Deprecated('Use userEventsTotalPages instead')
  RxInt get maxUserPage => userEventsTotalPages;

  UserModelLatest? getLocalUser() {
    final box = Get.find<GetStorage>();
    Rx<UserModelLatest> user = UserModelLatest().obs;
    final usr = box.read(CacheKeys.user);
    if (usr != null) {
      user(UserModelLatest.fromJson(usr));
      return user.value;
    } else {
      return null;
    }
  }

  /// Reset events pagination state
  void resetEvents() {
    events.clear();
    eventsCurrentPage.value = 0;
    eventsHasMoreData.value = true;
    eventsTotalPages.value = 0;
    eventsTotal.value = 0;
  }

  /// Reset user events pagination state
  void resetUserEvents() {
    userEvents.clear();
    userEventsCurrentPage.value = 0;
    userEventsHasMoreData.value = true;
    userEventsTotalPages.value = 0;
    userEventsTotal.value = 0;
  }

  /// Build query parameters for API requests
  Map<String, String> _buildQueryParams({
    required int page,
    required int size,
    String? phoneNumber,
  }) {
    final params = <String, String>{
      'page': page.toString(),
      'size': size.toString(),
    };

    if (phoneNumber != null && phoneNumber.isNotEmpty) {
      // For user events endpoint
    }

    if (search.value.isNotEmpty) {
      params['search'] = search.value;
    }

    if (filterCategory.value != null) {
      params['category_id'] = filterCategory.value.toString();
    }

    if (startDate.value.isNotEmpty) {
      params['start-date'] = startDate.value;
    }

    if (endDate.value.isNotEmpty) {
      params['end-date'] = endDate.value;
    }

    if (status.value.isNotEmpty) {
      params['status'] = status.value;
    }

    return params;
  }

  /// Main method to fetch events with pagination
  Future<void> fetchEvents({
    int? page,
    bool isRefresh = false,
  }) async {
    // Determine if this is first page load
    final isFirstPage = (page ?? eventsCurrentPage.value) == 0;

    if (isFirstPage) {
      eventsLoading(true);
      if (isRefresh) resetEvents();
    } else {
      eventsLoadingMore(true);
    }

    try {
      final effectivePage = page ?? eventsCurrentPage.value;

      // Check if we have more data to load
      if (!isFirstPage && !eventsHasMoreData.value) {
        logger.i("No more events to load");
        return;
      }

      // Build query parameters
      final queryParams = _buildQueryParams(
        page: effectivePage,
        size: eventsPageSize.value,
      );

      // Build URL with query parameters
      final uri = Uri.parse(ApiUrls.GETALLEVENTS)
          .replace(queryParameters: queryParams);

      logger.i("Fetching events: page=$effectivePage, params=$queryParams");

      final response = await apiProvider.request(
        method: Method.GET,
        url: uri.toString(),
      );

      if (response.data != null && response.data['data'] != null) {
        _handleEventsResponse(response.data['data'], effectivePage, isFirstPage);
      } else {
        logger.w('No data in events response');
        if (isFirstPage) {
          events.clear();
        }
        eventsHasMoreData.value = false;
      }
    } catch (e) {
      logger.e('Error fetching events: $e');
      if (Get.context != null) {
        ToastUtils.showErrorToast(Get.context!, 'Error', 'Failed to fetch events: ${e.toString()}');
      }
    } finally {
      if (isFirstPage) {
        eventsLoading(false);
      } else {
        eventsLoadingMore(false);
      }
    }
  }

  /// Handle events API response
  void _handleEventsResponse(Map<String, dynamic> data, int requestedPage, bool isFirstPage) {
    // Update pagination metadata
    eventsTotalPages.value = data['total_pages'] ?? 0;
    eventsTotal.value = data['total'] ?? 0;
    eventsCurrentPage.value = requestedPage + 1;

    // Check if this is the last page
    final isLastPage = data['last'] ?? false;
    eventsHasMoreData.value = !isLastPage;

    logger.i("Events pagination: page=${requestedPage + 1}/${eventsTotalPages.value}, total=${eventsTotal.value}, hasMore=${eventsHasMoreData.value}");

    // Parse events
    final eventsData = (data["items"] ?? []) as List<dynamic>;
    if (eventsData.isNotEmpty) {
      final newEvents = eventsData
          .map((element) => Event.fromJson(element))
          .toList();

      if (isFirstPage) {
        events.value = newEvents;
      } else {
        events.addAll(newEvents);
      }

      logger.i("Added ${newEvents.length} events. Total: ${events.length}/${eventsTotal.value}");
    } else {
      eventsHasMoreData.value = false;
      logger.i("No events returned for page $requestedPage");
    }
  }

  /// Main method to fetch user events with pagination
  Future<void> fetchUserEvents({
    int? page,
    bool isRefresh = false,
  }) async {
    final user = getLocalUser();
    if (user?.phoneNumber == null) {
      logger.w('No user phone number available for fetching user events');
      return;
    }

    // Determine if this is first page load
    final isFirstPage = (page ?? userEventsCurrentPage.value) == 0;

    if (isFirstPage) {
      userEventsLoading(true);
      if (isRefresh) resetUserEvents();
    } else {
      userEventsLoadingMore(true);
    }

    try {
      final effectivePage = page ?? userEventsCurrentPage.value;

      // Check if we have more data to load
      if (!isFirstPage && !userEventsHasMoreData.value) {
        logger.i("No more user events to load");
        return;
      }

      // Build query parameters
      final queryParams = _buildQueryParams(
        page: effectivePage,
        size: userEventsPageSize.value,
        phoneNumber: user!.phoneNumber,
      );

      // Build URL with query parameters
      final baseUrl = "${ApiUrls.GETUSEREVENTS}/${user.phoneNumber}";
      final uri = Uri.parse(baseUrl)
          .replace(queryParameters: queryParams);

      logger.i("Fetching user events: page=$effectivePage, params=$queryParams");

      final response = await apiProvider.request(
        method: Method.GET,
        url: uri.toString(),
      );

      if (response.data['status'] ?? false) {
        if (response.data['data'] != null) {
          _handleUserEventsResponse(response.data['data'], effectivePage, isFirstPage);
        } else {
          logger.w('No data in user events response');
          if (isFirstPage) {
            userEvents.clear();
          }
          userEventsHasMoreData.value = false;
        }
      } else {
        final message = response.data['message'] ?? 'Error retrieving user events';
        logger.e('User events API error: $message');
        if (Get.context != null) {
          ToastUtils.showErrorToast(Get.context!, 'Error', message);
        }
      }
    } catch (e) {
      logger.e('Error fetching user events: $e');
      if (Get.context != null) {
        ToastUtils.showErrorToast(Get.context!, 'Error', 'Failed to fetch user events: ${e.toString()}');
      }
    } finally {
      if (isFirstPage) {
        userEventsLoading(false);
      } else {
        userEventsLoadingMore(false);
      }
    }
  }

  /// Handle user events API response
  void _handleUserEventsResponse(Map<String, dynamic> data, int requestedPage, bool isFirstPage) {
    // Update pagination metadata
    userEventsTotalPages.value = data['total_pages'] ?? 0;
    userEventsTotal.value = data['total'] ?? 0;
    userEventsCurrentPage.value = requestedPage + 1;

    // Check if this is the last page
    final isLastPage = data['last'] ?? false;
    userEventsHasMoreData.value = !isLastPage;

    logger.i("User events pagination: page=${requestedPage + 1}/${userEventsTotalPages.value}, total=${userEventsTotal.value}, hasMore=${userEventsHasMoreData.value}");

    // Parse user events
    final eventsData = (data["items"] ?? []) as List<dynamic>;
    if (eventsData.isNotEmpty) {
      final newEvents = eventsData
          .map((element) => MyEventsModel.fromJson(element))
          .toList();

      if (isFirstPage) {
        userEvents.value = newEvents;
      } else {
        userEvents.addAll(newEvents);
      }

      logger.i("Added ${newEvents.length} user events. Total: ${userEvents.length}/${userEventsTotal.value}");
    } else {
      userEventsHasMoreData.value = false;
      logger.i("No user events returned for page $requestedPage");
    }
  }

  /// Load initial events
  Future<void> loadEvents() async {
    await fetchEvents(page: 0, isRefresh: true);
  }

  /// Load more events (for infinite scroll)
  Future<void> loadMoreEvents() async {
    if (eventsLoadingMore.value || !eventsHasMoreData.value) {
      logger.i("Skipping loadMoreEvents: loading=${eventsLoadingMore.value}, hasMore=${eventsHasMoreData.value}");
      return;
    }

    final nextPage = eventsCurrentPage.value;
    logger.i("Loading more events: page $nextPage");
    await fetchEvents(page: nextPage);
  }

  /// Refresh events
  Future<void> refreshEvents() async {
    resetEvents();
    await fetchEvents(page: 0, isRefresh: true);
  }

  /// Load initial user events
  Future<void> loadUserEvents() async {
    await fetchUserEvents(page: 0, isRefresh: true);
  }

  /// Load more user events (for infinite scroll)
  Future<void> loadMoreUserEvents() async {
    if (userEventsLoadingMore.value || !userEventsHasMoreData.value) {
      logger.i("Skipping loadMoreUserEvents: loading=${userEventsLoadingMore.value}, hasMore=${userEventsHasMoreData.value}");
      return;
    }

    final nextPage = userEventsCurrentPage.value;
    logger.i("Loading more user events: page $nextPage");
    await fetchUserEvents(page: nextPage);
  }

  /// Refresh user events
  Future<void> refreshUserEvents() async {
    resetUserEvents();
    await fetchUserEvents(page: 0, isRefresh: true);
  }

  /// Apply filters and refresh data
  Future<void> applyFilters({
    String? searchQuery,
    int? categoryId,
    String? startDateFilter,
    String? endDateFilter,
    String? statusFilter,
  }) async {
    isApplyingFilters(true);

    try {
      // Update filter values
      if (searchQuery != null) search.value = searchQuery;
      if (categoryId != null) filterCategory.value = categoryId;
      if (startDateFilter != null) startDate.value = startDateFilter;
      if (endDateFilter != null) endDate.value = endDateFilter;
      if (statusFilter != null) status.value = statusFilter;

      // Reset and reload both events and user events
      resetEvents();
      resetUserEvents();

      await Future.wait([
        fetchEvents(page: 0, isRefresh: true),
        fetchUserEvents(page: 0, isRefresh: true),
      ]);
    } finally {
      isApplyingFilters(false);
    }
  }

  /// Clear all filters
  Future<void> clearFilters() async {
    search.value = "";
    filterCategory.value = null;
    startDate.value = "";
    endDate.value = "";
    status.value = "";
    selectedCategory.value = null;

    await applyFilters();
  }
}
